import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/models/api_response.dart';
import '../entities/numbering_task.dart';

/// Repository interface for numbering management
abstract class NumberingRepository {
  /// Get all numbering tasks with optional filtering
  Future<Either<Failure, List<NumberingTask>>> getAllTasks({
    NumberingTaskStatus? status,
    String? orderNo,
    String? clientName,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Get a specific numbering task by ID
  Future<Either<Failure, NumberingTask>> getTaskById(String id);

  /// Create a new numbering task
  Future<Either<Failure, NumberingTask>> createTask(NumberingTask task);

  /// Update an existing numbering task
  Future<Either<Failure, NumberingTask>> updateTask(NumberingTask task);

  /// Delete a numbering task
  Future<Either<Failure, void>> deleteTask(String id);

  /// Get a new task (assign to current user)
  Future<Either<Failure, NumberingTask>> getNewTask(String userId);

  /// Complete a numbering task
  Future<Either<Failure, NumberingTask>> completeTask(String taskId, String userId);

  /// Send task to Munda department
  Future<Either<Failure, NumberingTask>> sendTaskToMunda(String taskId, String userId);

  /// Get tasks assigned to a specific user
  Future<Either<Failure, List<NumberingTask>>> getTasksAssignedToUser(String userId);

  /// Get tasks by status
  Future<Either<Failure, List<NumberingTask>>> getTasksByStatus(NumberingTaskStatus status);

  /// Get task statistics
  Future<Either<Failure, NumberingTaskStatistics>> getTaskStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  });

  /// Bulk update task status
  Future<Either<Failure, List<NumberingTask>>> bulkUpdateTaskStatus(
    List<String> taskIds,
    NumberingTaskStatus status,
    String userId,
  );

  /// Search tasks by criteria
  Future<Either<Failure, List<NumberingTask>>> searchTasks({
    String? searchQuery,
    NumberingTaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Get default rate per piece (configurable by admin)
  Future<Either<Failure, double>> getDefaultRatePerPiece();

  /// Update default rate per piece (admin only)
  Future<Either<Failure, void>> updateDefaultRatePerPiece(double rate, String adminUserId);

  /// Export tasks to CSV/Excel format
  Future<Either<Failure, String>> exportTasks({
    DateTime? startDate,
    DateTime? endDate,
    NumberingTaskStatus? status,
    String format = 'csv', // 'csv' or 'excel'
  });

  /// Get task history/audit trail
  Future<Either<Failure, List<NumberingTaskHistory>>> getTaskHistory(String taskId);

  /// Stream of real-time task updates
  Stream<Either<Failure, List<NumberingTask>>> watchTasks({
    NumberingTaskStatus? status,
    String? userId,
  });

  /// Stream of task statistics updates
  Stream<Either<Failure, NumberingTaskStatistics>> watchTaskStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  });
}

/// Task statistics model
class NumberingTaskStatistics {
  final int totalTasks;
  final int pendingTasks;
  final int inProgressTasks;
  final int completedTasks;
  final int sentToMundaTasks;
  final double totalAmount;
  final double averageRatePerPiece;
  final int totalPieces;
  final Map<String, int> tasksByDate;
  final Map<String, int> tasksByUser;

  const NumberingTaskStatistics({
    required this.totalTasks,
    required this.pendingTasks,
    required this.inProgressTasks,
    required this.completedTasks,
    required this.sentToMundaTasks,
    required this.totalAmount,
    required this.averageRatePerPiece,
    required this.totalPieces,
    required this.tasksByDate,
    required this.tasksByUser,
  });

  double get completionRate => totalTasks > 0 ? (completedTasks + sentToMundaTasks) / totalTasks : 0.0;
  double get progressRate => totalTasks > 0 ? inProgressTasks / totalTasks : 0.0;
}

/// Task history/audit trail model
class NumberingTaskHistory {
  final String id;
  final String taskId;
  final String action;
  final String userId;
  final String userName;
  final DateTime timestamp;
  final Map<String, dynamic>? oldValues;
  final Map<String, dynamic>? newValues;
  final String? notes;

  const NumberingTaskHistory({
    required this.id,
    required this.taskId,
    required this.action,
    required this.userId,
    required this.userName,
    required this.timestamp,
    this.oldValues,
    this.newValues,
    this.notes,
  });
}

/// Request models for repository operations
class CreateNumberingTaskRequest {
  final DateTime date;
  final String orderNo;
  final String clientName;
  final String designNo;
  final GarmentSize size;
  final String color;
  final String bundleNo;
  final int pieceStartNo;
  final int pieceEndNo;
  final double? ratePerPiece; // Optional, will use default if not provided
  final String checkedBy;
  final String remarks;

  const CreateNumberingTaskRequest({
    required this.date,
    required this.orderNo,
    required this.clientName,
    required this.designNo,
    required this.size,
    required this.color,
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    this.ratePerPiece,
    required this.checkedBy,
    required this.remarks,
  });
}

class UpdateNumberingTaskRequest {
  final String id;
  final DateTime? date;
  final String? orderNo;
  final String? clientName;
  final String? designNo;
  final GarmentSize? size;
  final String? color;
  final String? bundleNo;
  final int? pieceStartNo;
  final int? pieceEndNo;
  final double? ratePerPiece;
  final String? checkedBy;
  final String? remarks;
  final NumberingTaskStatus? status;

  const UpdateNumberingTaskRequest({
    required this.id,
    this.date,
    this.orderNo,
    this.clientName,
    this.designNo,
    this.size,
    this.color,
    this.bundleNo,
    this.pieceStartNo,
    this.pieceEndNo,
    this.ratePerPiece,
    this.checkedBy,
    this.remarks,
    this.status,
  });
}
