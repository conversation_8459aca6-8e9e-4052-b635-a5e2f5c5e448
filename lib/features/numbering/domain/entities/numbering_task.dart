import 'package:equatable/equatable.dart';
import '../../../../shared/models/base_entity.dart';
import '../../../../shared/enums/common_enums.dart';

/// Garment size enumeration
enum GarmentSize {
  xs('XS'),
  s('S'),
  m('M'),
  l('L'),
  xl('XL'),
  xxl('XXL'),
  xxxl('XXXL');

  const GarmentSize(this.value);
  final String value;

  String get displayName => value;

  static GarmentSize fromString(String value) {
    return GarmentSize.values.firstWhere(
      (size) => size.value.toLowerCase() == value.toLowerCase(),
      orElse: () => GarmentSize.m,
    );
  }

  static List<String> get allSizes => GarmentSize.values.map((e) => e.value).toList();
}

/// Numbering task status enumeration
enum NumberingTaskStatus {
  pending('pending'),
  inProgress('in_progress'),
  completed('completed'),
  sentToMunda('sent_to_munda');

  const NumberingTaskStatus(this.value);
  final String value;

  String get displayName {
    switch (this) {
      case NumberingTaskStatus.pending:
        return 'Pending';
      case NumberingTaskStatus.inProgress:
        return 'In Progress';
      case NumberingTaskStatus.completed:
        return 'Completed';
      case NumberingTaskStatus.sentToMunda:
        return 'Sent to Munda';
    }
  }

  String get colorCode {
    switch (this) {
      case NumberingTaskStatus.pending:
        return '#F59E0B'; // Amber
      case NumberingTaskStatus.inProgress:
        return '#3B82F6'; // Blue
      case NumberingTaskStatus.completed:
        return '#10B981'; // Green
      case NumberingTaskStatus.sentToMunda:
        return '#8B5CF6'; // Purple
    }
  }

  bool get isActive => this == NumberingTaskStatus.inProgress;
  bool get isCompleted => this == NumberingTaskStatus.completed || this == NumberingTaskStatus.sentToMunda;

  static NumberingTaskStatus fromString(String value) {
    return NumberingTaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => NumberingTaskStatus.pending,
    );
  }
}

/// Numbering task entity
class NumberingTask extends BaseEntity {
  final String id;
  final DateTime date;
  final String orderNo;
  final String clientName;
  final String designNo;
  final GarmentSize size;
  final String color;
  final String bundleNo;
  final int pieceStartNo;
  final int pieceEndNo;
  final double ratePerPiece;
  final String checkedBy;
  final String remarks;
  final NumberingTaskStatus status;
  final String? assignedTo;
  final DateTime? completedAt;
  final DateTime? sentToMundaAt;

  const NumberingTask({
    required this.id,
    required this.date,
    required this.orderNo,
    required this.clientName,
    required this.designNo,
    required this.size,
    required this.color,
    required this.bundleNo,
    required this.pieceStartNo,
    required this.pieceEndNo,
    required this.ratePerPiece,
    required this.checkedBy,
    required this.remarks,
    required this.status,
    this.assignedTo,
    this.completedAt,
    this.sentToMundaAt,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String createdBy,
    required String updatedBy,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          createdBy: createdBy,
          updatedBy: updatedBy,
        );

  /// Calculate total pieces in the bundle
  int get totalPieces => pieceEndNo - pieceStartNo + 1;

  /// Calculate total amount for the bundle
  double get totalAmount => totalPieces * ratePerPiece;

  /// Check if task can be completed
  bool get canBeCompleted => status == NumberingTaskStatus.inProgress;

  /// Check if task can be sent to Munda
  bool get canBeSentToMunda => status == NumberingTaskStatus.completed;

  /// Check if task is editable
  bool get isEditable => status == NumberingTaskStatus.pending || status == NumberingTaskStatus.inProgress;

  @override
  List<Object?> get props => [
        id,
        date,
        orderNo,
        clientName,
        designNo,
        size,
        color,
        bundleNo,
        pieceStartNo,
        pieceEndNo,
        ratePerPiece,
        checkedBy,
        remarks,
        status,
        assignedTo,
        completedAt,
        sentToMundaAt,
        ...super.props,
      ];

  /// Create a copy of the task with updated fields
  NumberingTask copyWith({
    String? id,
    DateTime? date,
    String? orderNo,
    String? clientName,
    String? designNo,
    GarmentSize? size,
    String? color,
    String? bundleNo,
    int? pieceStartNo,
    int? pieceEndNo,
    double? ratePerPiece,
    String? checkedBy,
    String? remarks,
    NumberingTaskStatus? status,
    String? assignedTo,
    DateTime? completedAt,
    DateTime? sentToMundaAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return NumberingTask(
      id: id ?? this.id,
      date: date ?? this.date,
      orderNo: orderNo ?? this.orderNo,
      clientName: clientName ?? this.clientName,
      designNo: designNo ?? this.designNo,
      size: size ?? this.size,
      color: color ?? this.color,
      bundleNo: bundleNo ?? this.bundleNo,
      pieceStartNo: pieceStartNo ?? this.pieceStartNo,
      pieceEndNo: pieceEndNo ?? this.pieceEndNo,
      ratePerPiece: ratePerPiece ?? this.ratePerPiece,
      checkedBy: checkedBy ?? this.checkedBy,
      remarks: remarks ?? this.remarks,
      status: status ?? this.status,
      assignedTo: assignedTo ?? this.assignedTo,
      completedAt: completedAt ?? this.completedAt,
      sentToMundaAt: sentToMundaAt ?? this.sentToMundaAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  @override
  String toString() {
    return 'NumberingTask(id: $id, orderNo: $orderNo, bundleNo: $bundleNo, status: $status)';
  }
}
