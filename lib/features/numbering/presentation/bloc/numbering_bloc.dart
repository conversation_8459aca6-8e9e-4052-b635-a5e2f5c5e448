import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/auth/entities/user_entities.dart';
import '../../../../core/auth/providers/auth_provider.dart';
import '../../domain/entities/numbering_task.dart';
import '../../domain/usecases/numbering_usecases.dart';
import 'numbering_event.dart';
import 'numbering_state.dart';

/// BLoC for managing numbering tasks
@injectable
class NumberingBloc extends Bloc<NumberingEvent, NumberingState> {
  final GetAllNumberingTasksUseCase _getAllTasksUseCase;
  final GetNumberingTaskByIdUseCase _getTaskByIdUseCase;
  final CreateNumberingTaskUseCase _createTaskUseCase;
  final UpdateNumberingTaskUseCase _updateTaskUseCase;
  final DeleteNumberingTaskUseCase _deleteTaskUseCase;
  final GetNewTaskUseCase _getNewTaskUseCase;
  final CompleteTaskUseCase _completeTaskUseCase;
  final SendTaskToMundaUseCase _sendTaskToMundaUseCase;
  final GetTaskStatisticsUseCase _getStatisticsUseCase;
  final SearchTasksUseCase _searchTasksUseCase;
  final AuthProvider _authProvider;

  StreamSubscription<List<NumberingTask>>? _tasksStreamSubscription;

  NumberingBloc(
    this._getAllTasksUseCase,
    this._getTaskByIdUseCase,
    this._createTaskUseCase,
    this._updateTaskUseCase,
    this._deleteTaskUseCase,
    this._getNewTaskUseCase,
    this._completeTaskUseCase,
    this._sendTaskToMundaUseCase,
    this._getStatisticsUseCase,
    this._searchTasksUseCase,
    this._authProvider,
  ) : super(const NumberingInitial()) {
    on<LoadNumberingTasks>(_onLoadNumberingTasks);
    on<LoadNumberingTaskById>(_onLoadNumberingTaskById);
    on<CreateNumberingTask>(_onCreateNumberingTask);
    on<UpdateNumberingTask>(_onUpdateNumberingTask);
    on<DeleteNumberingTask>(_onDeleteNumberingTask);
    on<GetNewNumberingTask>(_onGetNewNumberingTask);
    on<CompleteNumberingTask>(_onCompleteNumberingTask);
    on<SendTaskToMunda>(_onSendTaskToMunda);
    on<SearchNumberingTasks>(_onSearchNumberingTasks);
    on<LoadMyNumberingTasks>(_onLoadMyNumberingTasks);
    on<LoadTasksByStatus>(_onLoadTasksByStatus);
    on<LoadTaskStatistics>(_onLoadTaskStatistics);
    on<RefreshNumberingTasks>(_onRefreshNumberingTasks);
    on<ToggleTaskSelection>(_onToggleTaskSelection);
    on<SelectAllTasks>(_onSelectAllTasks);
    on<DeselectAllTasks>(_onDeselectAllTasks);
    on<ClearTaskSelection>(_onClearTaskSelection);
    on<BulkUpdateTaskStatus>(_onBulkUpdateTaskStatus);
    on<ResetNumberingState>(_onResetNumberingState);
    on<TasksUpdatedFromStream>(_onTasksUpdatedFromStream);
    on<StartWatchingTasks>(_onStartWatchingTasks);
    on<StopWatchingTasks>(_onStopWatchingTasks);
  }

  @override
  Future<void> close() {
    _tasksStreamSubscription?.cancel();
    return super.close();
  }

  /// Get current user ID
  String? get _currentUserId => _authProvider.user?.id;

  /// Load numbering tasks
  Future<void> _onLoadNumberingTasks(
    LoadNumberingTasks event,
    Emitter<NumberingState> emit,
  ) async {
    if (!event.forceRefresh && state is NumberingTasksLoaded) {
      return; // Don't reload if already loaded unless forced
    }

    emit(const NumberingLoading(message: 'Loading numbering tasks...'));

    final params = GetAllTasksParams(
      status: event.status,
      orderNo: event.orderNo,
      clientName: event.clientName,
      startDate: event.startDate,
      endDate: event.endDate,
      limit: event.limit,
      offset: event.offset,
    );

    final result = await _getAllTasksUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (tasks) {
        if (tasks.isEmpty) {
          emit(const NumberingTasksEmpty());
        } else {
          emit(NumberingTasksLoaded(
            tasks: tasks,
            totalCount: tasks.length,
            hasReachedMax: event.limit == null || tasks.length < (event.limit ?? 0),
          ));
        }
      },
    );
  }

  /// Load a specific task by ID
  Future<void> _onLoadNumberingTaskById(
    LoadNumberingTaskById event,
    Emitter<NumberingState> emit,
  ) async {
    emit(const NumberingLoading(message: 'Loading task details...'));

    final result = await _getTaskByIdUseCase(event.taskId);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) => emit(NumberingTaskLoaded(task)),
    );
  }

  /// Create a new numbering task
  Future<void> _onCreateNumberingTask(
    CreateNumberingTask event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(const NumberingLoading(message: 'Creating numbering task...'));

    final params = CreateTaskParams(
      date: event.date,
      orderNo: event.orderNo,
      clientName: event.clientName,
      designNo: event.designNo,
      size: event.size,
      color: event.color,
      bundleNo: event.bundleNo,
      pieceStartNo: event.pieceStartNo,
      pieceEndNo: event.pieceEndNo,
      ratePerPiece: event.ratePerPiece ?? 2.0,
      checkedBy: event.checkedBy,
      remarks: event.remarks,
      createdBy: _currentUserId!,
    );

    final result = await _createTaskUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) {
        emit(NumberingTaskOperationSuccess(
          message: 'Task created successfully',
          task: task,
          operationType: 'create',
        ));
        // Reload tasks to show the new one
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Update an existing numbering task
  Future<void> _onUpdateNumberingTask(
    UpdateNumberingTask event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(const NumberingLoading(message: 'Updating numbering task...'));

    final params = UpdateTaskParams(
      taskId: event.taskId,
      date: event.date,
      orderNo: event.orderNo,
      clientName: event.clientName,
      designNo: event.designNo,
      size: event.size,
      color: event.color,
      bundleNo: event.bundleNo,
      pieceStartNo: event.pieceStartNo,
      pieceEndNo: event.pieceEndNo,
      ratePerPiece: event.ratePerPiece,
      checkedBy: event.checkedBy,
      remarks: event.remarks,
      status: event.status,
      updatedBy: _currentUserId!,
    );

    final result = await _updateTaskUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) {
        emit(NumberingTaskOperationSuccess(
          message: 'Task updated successfully',
          task: task,
          operationType: 'update',
        ));
        // Reload tasks to show the updated one
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Delete a numbering task
  Future<void> _onDeleteNumberingTask(
    DeleteNumberingTask event,
    Emitter<NumberingState> emit,
  ) async {
    emit(const NumberingLoading(message: 'Deleting numbering task...'));

    final result = await _deleteTaskUseCase(event.taskId);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (_) {
        emit(const NumberingTaskOperationSuccess(
          message: 'Task deleted successfully',
          operationType: 'delete',
        ));
        // Reload tasks to remove the deleted one
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Get a new task (assign to current user)
  Future<void> _onGetNewNumberingTask(
    GetNewNumberingTask event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(const NumberingLoading(message: 'Getting new task...'));

    final result = await _getNewTaskUseCase(_currentUserId!);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) {
        emit(NumberingTaskOperationSuccess(
          message: 'New task assigned successfully',
          task: task,
          operationType: 'get_task',
        ));
        // Reload tasks to show the updated status
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Complete a numbering task
  Future<void> _onCompleteNumberingTask(
    CompleteNumberingTask event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(const NumberingLoading(message: 'Completing task...'));

    final params = CompleteTaskParams(
      taskId: event.taskId,
      userId: _currentUserId!,
    );

    final result = await _completeTaskUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) {
        emit(NumberingTaskOperationSuccess(
          message: 'Task completed successfully',
          task: task,
          operationType: 'complete',
        ));
        // Reload tasks to show the updated status
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Send task to Munda department
  Future<void> _onSendTaskToMunda(
    SendTaskToMunda event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(const NumberingLoading(message: 'Sending task to Munda...'));

    final params = SendToMundaParams(
      taskId: event.taskId,
      userId: _currentUserId!,
    );

    final result = await _sendTaskToMundaUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (task) {
        emit(NumberingTaskOperationSuccess(
          message: 'Task sent to Munda successfully',
          task: task,
          operationType: 'send_to_munda',
        ));
        // Reload tasks to show the updated status
        add(const LoadNumberingTasks(forceRefresh: true));
      },
    );
  }

  /// Search numbering tasks
  Future<void> _onSearchNumberingTasks(
    SearchNumberingTasks event,
    Emitter<NumberingState> emit,
  ) async {
    if (state is NumberingTasksLoaded) {
      emit(NumberingSearching(
        searchQuery: event.searchQuery ?? '',
        currentTasks: (state as NumberingTasksLoaded).tasks,
      ));
    } else {
      emit(const NumberingLoading(message: 'Searching tasks...'));
    }

    final params = SearchTasksParams(
      searchQuery: event.searchQuery,
      status: event.status,
      startDate: event.startDate,
      endDate: event.endDate,
      limit: event.limit,
      offset: event.offset,
    );

    final result = await _searchTasksUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (tasks) {
        emit(NumberingSearchResults(
          searchQuery: event.searchQuery ?? '',
          results: tasks,
          totalCount: tasks.length,
        ));
      },
    );
  }

  /// Load tasks assigned to current user
  Future<void> _onLoadMyNumberingTasks(
    LoadMyNumberingTasks event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    add(LoadNumberingTasks(
      // This would need to be implemented to filter by assigned user
      forceRefresh: true,
    ));
  }

  /// Load tasks by status
  Future<void> _onLoadTasksByStatus(
    LoadTasksByStatus event,
    Emitter<NumberingState> emit,
  ) async {
    add(LoadNumberingTasks(
      status: event.status,
      forceRefresh: true,
    ));
  }

  /// Load task statistics
  Future<void> _onLoadTaskStatistics(
    LoadTaskStatistics event,
    Emitter<NumberingState> emit,
  ) async {
    emit(const NumberingLoading(message: 'Loading statistics...'));

    final params = GetStatisticsParams(
      startDate: event.startDate,
      endDate: event.endDate,
      userId: event.userId,
    );

    final result = await _getStatisticsUseCase(params);

    result.fold(
      (failure) => emit(NumberingError(message: failure.message)),
      (statistics) => emit(NumberingStatisticsLoaded(statistics)),
    );
  }

  /// Refresh numbering tasks
  Future<void> _onRefreshNumberingTasks(
    RefreshNumberingTasks event,
    Emitter<NumberingState> emit,
  ) async {
    if (state is NumberingTasksLoaded) {
      emit(NumberingRefreshing((state as NumberingTasksLoaded).tasks));
    }

    add(const LoadNumberingTasks(forceRefresh: true));
  }

  /// Toggle task selection
  void _onToggleTaskSelection(
    ToggleTaskSelection event,
    Emitter<NumberingState> emit,
  ) {
    if (state is NumberingTasksLoaded) {
      final currentState = state as NumberingTasksLoaded;
      final selectedIds = Set<String>.from(currentState.selectedTaskIds);

      if (selectedIds.contains(event.taskId)) {
        selectedIds.remove(event.taskId);
      } else {
        selectedIds.add(event.taskId);
      }

      emit(currentState.copyWith(selectedTaskIds: selectedIds));
    }
  }

  /// Select all tasks
  void _onSelectAllTasks(
    SelectAllTasks event,
    Emitter<NumberingState> emit,
  ) {
    if (state is NumberingTasksLoaded) {
      final currentState = state as NumberingTasksLoaded;
      final allTaskIds = currentState.tasks.map((task) => task.id).toSet();
      emit(currentState.copyWith(selectedTaskIds: allTaskIds));
    }
  }

  /// Deselect all tasks
  void _onDeselectAllTasks(
    DeselectAllTasks event,
    Emitter<NumberingState> emit,
  ) {
    if (state is NumberingTasksLoaded) {
      final currentState = state as NumberingTasksLoaded;
      emit(currentState.copyWith(selectedTaskIds: const <String>{}));
    }
  }

  /// Clear task selection
  void _onClearTaskSelection(
    ClearTaskSelection event,
    Emitter<NumberingState> emit,
  ) {
    if (state is NumberingTasksLoaded) {
      final currentState = state as NumberingTasksLoaded;
      emit(currentState.copyWith(selectedTaskIds: const <String>{}));
    }
  }

  /// Bulk update task status
  Future<void> _onBulkUpdateTaskStatus(
    BulkUpdateTaskStatus event,
    Emitter<NumberingState> emit,
  ) async {
    if (_currentUserId == null) {
      emit(const NumberingError(message: 'User not authenticated'));
      return;
    }

    emit(NumberingBulkOperationInProgress(
      operationType: 'bulk_update_status',
      taskIds: event.taskIds,
      processedCount: 0,
      totalCount: event.taskIds.length,
    ));

    // This would need to be implemented in the use case
    // For now, just show completion
    emit(NumberingBulkOperationCompleted(
      operationType: 'bulk_update_status',
      successCount: event.taskIds.length,
      failureCount: 0,
      failedTaskIds: [],
    ));

    // Reload tasks
    add(const LoadNumberingTasks(forceRefresh: true));
  }

  /// Reset numbering state
  void _onResetNumberingState(
    ResetNumberingState event,
    Emitter<NumberingState> emit,
  ) {
    _tasksStreamSubscription?.cancel();
    emit(const NumberingInitial());
  }

  /// Handle real-time task updates from stream
  void _onTasksUpdatedFromStream(
    TasksUpdatedFromStream event,
    Emitter<NumberingState> emit,
  ) {
    if (state is NumberingTasksLoaded) {
      final currentState = state as NumberingTasksLoaded;
      emit(currentState.copyWith(tasks: event.tasks));
    } else {
      emit(NumberingTasksLoaded(
        tasks: event.tasks,
        totalCount: event.tasks.length,
      ));
    }
  }

  /// Start watching tasks for real-time updates
  void _onStartWatchingTasks(
    StartWatchingTasks event,
    Emitter<NumberingState> emit,
  ) {
    _tasksStreamSubscription?.cancel();

    // This would need to be implemented in the repository
    // For now, just emit the current state
    if (state is NumberingTasksLoaded) {
      emit(NumberingWatchingTasks(
        tasks: (state as NumberingTasksLoaded).tasks,
        statusFilter: event.status,
        userFilter: event.userId,
      ));
    }
  }

  /// Stop watching tasks
  void _onStopWatchingTasks(
    StopWatchingTasks event,
    Emitter<NumberingState> emit,
  ) {
    _tasksStreamSubscription?.cancel();
    
    if (state is NumberingWatchingTasks) {
      final watchingState = state as NumberingWatchingTasks;
      emit(NumberingTasksLoaded(
        tasks: watchingState.tasks,
        totalCount: watchingState.tasks.length,
      ));
    }
  }
}
