import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/widgets/animated_counter.dart';
import '../../domain/repositories/numbering_repository.dart';

/// Widget to display numbering task statistics
class NumberingStatisticsCard extends StatelessWidget {
  final NumberingTaskStatistics statistics;

  const NumberingStatisticsCard({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Statistics',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: 24),

          // Overview cards
          _buildOverviewCards(),
          const SizedBox(height: 24),

          // Progress indicators
          _buildProgressSection(),
          const SizedBox(height: 24),

          // Financial summary
          _buildFinancialSection(),
          const SizedBox(height: 24),

          // Charts section (placeholder)
          _buildChartsSection(),
        ],
      ),
    );
  }

  Widget _buildOverviewCards() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'Total Tasks',
          value: statistics.totalTasks,
          icon: Icons.assignment,
          color: AppColors.primary,
        ),
        _buildStatCard(
          title: 'Pending',
          value: statistics.pendingTasks,
          icon: Icons.pending,
          color: Colors.orange,
        ),
        _buildStatCard(
          title: 'In Progress',
          value: statistics.inProgressTasks,
          icon: Icons.play_circle,
          color: Colors.blue,
        ),
        _buildStatCard(
          title: 'Completed',
          value: statistics.completedTasks,
          icon: Icons.check_circle,
          color: Colors.green,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required int value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            AnimatedIntegerCounter(
              value: value,
              textStyle: AppTextStyles.headlineMedium.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.labelMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Progress Overview',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 16),

            // Completion rate
            _buildProgressIndicator(
              label: 'Completion Rate',
              value: statistics.completionRate,
              color: Colors.green,
            ),
            const SizedBox(height: 12),

            // Progress rate
            _buildProgressIndicator(
              label: 'In Progress Rate',
              value: statistics.progressRate,
              color: Colors.blue,
            ),
            const SizedBox(height: 12),

            // Pending rate
            _buildProgressIndicator(
              label: 'Pending Rate',
              value: statistics.totalTasks > 0 
                  ? statistics.pendingTasks / statistics.totalTasks 
                  : 0.0,
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator({
    required String label,
    required double value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label, style: AppTextStyles.bodyMedium),
            AnimatedPercentageCounter(
              percentage: value * 100,
              textStyle: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: value,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildFinancialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Summary',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildFinancialItem(
                    label: 'Total Amount',
                    value: statistics.totalAmount,
                    icon: Icons.currency_rupee,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildFinancialItem(
                    label: 'Avg Rate/Piece',
                    value: statistics.averageRatePerPiece,
                    icon: Icons.trending_up,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildFinancialItem(
              label: 'Total Pieces',
              value: statistics.totalPieces.toDouble(),
              icon: Icons.inventory,
              color: Colors.purple,
              isInteger: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialItem({
    required String label,
    required double value,
    required IconData icon,
    required Color color,
    bool isInteger = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.labelMedium,
                ),
                const SizedBox(height: 4),
                if (isInteger)
                  AnimatedIntegerCounter(
                    value: value.toInt(),
                    textStyle: AppTextStyles.titleMedium.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                else
                  AnimatedCurrencyCounter(
                    amount: value,
                    currency: '₹',
                    textStyle: AppTextStyles.titleMedium.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Task Distribution',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 16),

            // Status distribution
            _buildStatusDistribution(),
            const SizedBox(height: 24),

            // Recent activity placeholder
            Text(
              'Recent Activity',
              style: AppTextStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.outline),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.bar_chart, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'Charts coming soon',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDistribution() {
    final total = statistics.totalTasks;
    if (total == 0) {
      return const Center(
        child: Text('No data available'),
      );
    }

    return Column(
      children: [
        _buildStatusBar(
          'Pending',
          statistics.pendingTasks,
          total,
          Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildStatusBar(
          'In Progress',
          statistics.inProgressTasks,
          total,
          Colors.blue,
        ),
        const SizedBox(height: 8),
        _buildStatusBar(
          'Completed',
          statistics.completedTasks,
          total,
          Colors.green,
        ),
        const SizedBox(height: 8),
        _buildStatusBar(
          'Sent to Munda',
          statistics.sentToMundaTasks,
          total,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatusBar(String label, int count, int total, Color color) {
    final percentage = total > 0 ? count / total : 0.0;
    
    return Row(
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: AppTextStyles.bodySmall,
          ),
        ),
        Expanded(
          child: Container(
            height: 20,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage,
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 60,
          child: Text(
            '$count (${(percentage * 100).toStringAsFixed(1)}%)',
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}
