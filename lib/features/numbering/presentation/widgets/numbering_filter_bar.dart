import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../domain/entities/numbering_task.dart';
import '../bloc/numbering_bloc.dart';
import '../bloc/numbering_event.dart';

/// Filter bar widget for numbering tasks
class NumberingFilterBar extends StatefulWidget {
  const NumberingFilterBar({super.key});

  @override
  State<NumberingFilterBar> createState() => _NumberingFilterBarState();
}

class _NumberingFilterBarState extends State<NumberingFilterBar> {
  final TextEditingController _searchController = TextEditingController();
  NumberingTaskStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isExpanded = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(bottom: BorderSide(color: AppColors.outline)),
      ),
      child: Column(
        children: [
          // Search bar and basic filters
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by order, client, design, or bundle...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _performSearch();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: (value) => setState(() {}),
                  onSubmitted: (value) => _performSearch(),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: _performSearch,
                tooltip: 'Search',
              ),
              IconButton(
                icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                onPressed: () => setState(() => _isExpanded = !_isExpanded),
                tooltip: 'More Filters',
              ),
            ],
          ),

          // Expanded filters
          if (_isExpanded) ...[
            const SizedBox(height: 16),
            _buildExpandedFilters(),
          ],

          // Active filters display
          if (_hasActiveFilters()) ...[
            const SizedBox(height: 12),
            _buildActiveFilters(),
          ],
        ],
      ),
    );
  }

  Widget _buildExpandedFilters() {
    return Column(
      children: [
        // Status and date filters
        Row(
          children: [
            Expanded(
              child: _buildStatusFilter(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateRangeFilter(),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _clearAllFilters,
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear All'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _applyFilters,
                icon: const Icon(Icons.filter_list),
                label: const Text('Apply Filters'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return DropdownButtonFormField<NumberingTaskStatus?>(
      value: _selectedStatus,
      decoration: const InputDecoration(
        labelText: 'Status',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<NumberingTaskStatus?>(
          value: null,
          child: Text('All Statuses'),
        ),
        ...NumberingTaskStatus.values.map((status) {
          return DropdownMenuItem<NumberingTaskStatus?>(
            value: status,
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Color(int.parse(status.colorCode.substring(1), radix: 16) + 0xFF000000),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(status.displayName),
              ],
            ),
          );
        }),
      ],
      onChanged: (value) => setState(() => _selectedStatus = value),
    );
  }

  Widget _buildDateRangeFilter() {
    return InkWell(
      onTap: _selectDateRange,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date Range',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.date_range),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        child: Text(
          _getDateRangeText(),
          style: AppTextStyles.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final filters = <Widget>[];

    if (_searchController.text.isNotEmpty) {
      filters.add(_buildFilterChip(
        'Search: "${_searchController.text}"',
        () => setState(() => _searchController.clear()),
      ));
    }

    if (_selectedStatus != null) {
      filters.add(_buildFilterChip(
        'Status: ${_selectedStatus!.displayName}',
        () => setState(() => _selectedStatus = null),
      ));
    }

    if (_startDate != null || _endDate != null) {
      filters.add(_buildFilterChip(
        'Date: ${_getDateRangeText()}',
        () => setState(() {
          _startDate = null;
          _endDate = null;
        }),
      ));
    }

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        Text(
          'Active Filters:',
          style: AppTextStyles.labelMedium,
        ),
        ...filters,
      ],
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      onDeleted: onRemove,
      deleteIcon: const Icon(Icons.close, size: 16),
      backgroundColor: AppColors.primaryContainer,
      labelStyle: AppTextStyles.labelSmall,
    );
  }

  String _getDateRangeText() {
    if (_startDate == null && _endDate == null) {
      return 'Select date range';
    }
    
    final formatter = DateFormat('dd/MM/yyyy');
    if (_startDate != null && _endDate != null) {
      return '${formatter.format(_startDate!)} - ${formatter.format(_endDate!)}';
    } else if (_startDate != null) {
      return 'From ${formatter.format(_startDate!)}';
    } else if (_endDate != null) {
      return 'Until ${formatter.format(_endDate!)}';
    }
    
    return 'Select date range';
  }

  bool _hasActiveFilters() {
    return _searchController.text.isNotEmpty ||
           _selectedStatus != null ||
           _startDate != null ||
           _endDate != null;
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  void _performSearch() {
    final searchQuery = _searchController.text.trim();
    if (searchQuery.isNotEmpty) {
      context.read<NumberingBloc>().add(SearchNumberingTasks(
        searchQuery: searchQuery,
        status: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
      ));
    } else {
      _applyFilters();
    }
  }

  void _applyFilters() {
    context.read<NumberingBloc>().add(LoadNumberingTasks(
      status: _selectedStatus,
      startDate: _startDate,
      endDate: _endDate,
      forceRefresh: true,
    ));
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _selectedStatus = null;
      _startDate = null;
      _endDate = null;
    });
    
    context.read<NumberingBloc>().add(const LoadNumberingTasks(forceRefresh: true));
  }
}
