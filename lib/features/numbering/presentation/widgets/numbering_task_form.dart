import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/numbering_task.dart';

/// Form widget for creating and editing numbering tasks
class NumberingTaskForm extends StatefulWidget {
  final NumberingTask? task;
  final Function(Map<String, dynamic>) onSubmit;

  const NumberingTaskForm({
    super.key,
    this.task,
    required this.onSubmit,
  });

  @override
  State<NumberingTaskForm> createState() => _NumberingTaskFormState();
}

class _NumberingTaskFormState extends State<NumberingTaskForm> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Form controllers
  late final TextEditingController _orderNoController;
  late final TextEditingController _clientNameController;
  late final TextEditingController _designNoController;
  late final TextEditingController _colorController;
  late final TextEditingController _bundleNoController;
  late final TextEditingController _pieceStartNoController;
  late final TextEditingController _pieceEndNoController;
  late final TextEditingController _ratePerPieceController;
  late final TextEditingController _checkedByController;
  late final TextEditingController _remarksController;

  // Form values
  DateTime _selectedDate = DateTime.now();
  GarmentSize _selectedSize = GarmentSize.m;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _populateFormIfEditing();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _orderNoController.dispose();
    _clientNameController.dispose();
    _designNoController.dispose();
    _colorController.dispose();
    _bundleNoController.dispose();
    _pieceStartNoController.dispose();
    _pieceEndNoController.dispose();
    _ratePerPieceController.dispose();
    _checkedByController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _orderNoController = TextEditingController();
    _clientNameController = TextEditingController();
    _designNoController = TextEditingController();
    _colorController = TextEditingController();
    _bundleNoController = TextEditingController();
    _pieceStartNoController = TextEditingController();
    _pieceEndNoController = TextEditingController();
    _ratePerPieceController = TextEditingController(text: '2.00');
    _checkedByController = TextEditingController();
    _remarksController = TextEditingController();
  }

  void _populateFormIfEditing() {
    if (widget.task != null) {
      final task = widget.task!;
      _selectedDate = task.date;
      _orderNoController.text = task.orderNo;
      _clientNameController.text = task.clientName;
      _designNoController.text = task.designNo;
      _selectedSize = task.size;
      _colorController.text = task.color;
      _bundleNoController.text = task.bundleNo;
      _pieceStartNoController.text = task.pieceStartNo.toString();
      _pieceEndNoController.text = task.pieceEndNo.toString();
      _ratePerPieceController.text = task.ratePerPiece.toString();
      _checkedByController.text = task.checkedBy;
      _remarksController.text = task.remarks;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9,
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                widget.task == null ? 'Create New Task' : 'Edit Task',
                style: AppTextStyles.headlineSmall,
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Form
          Expanded(
            child: Form(
              key: _formKey,
              child: Scrollbar(
                controller: _scrollController,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    children: [
                      _buildFormFields(),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Action buttons
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // Row 1: Date and Order No
        Row(
          children: [
            Expanded(
              child: _buildDateField(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _orderNoController,
                label: 'Order No. *',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Order number is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Row 2: Client Name and Design No
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _clientNameController,
                label: 'Client Name *',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Client name is required';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _designNoController,
                label: 'Design No. *',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Design number is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Row 3: Size and Color
        Row(
          children: [
            Expanded(
              child: _buildSizeDropdown(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _colorController,
                label: 'Color *',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Color is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Row 4: Bundle No
        CustomTextField(
          controller: _bundleNoController,
          label: 'Bundle No. *',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Bundle number is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Row 5: Piece Start and End Numbers
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _pieceStartNoController,
                decoration: const InputDecoration(
                  labelText: 'Piece Start No. *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Start number is required';
                  }
                  final startNo = int.tryParse(value);
                  if (startNo == null || startNo <= 0) {
                    return 'Enter a valid start number';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _pieceEndNoController,
                decoration: const InputDecoration(
                  labelText: 'Piece End No. *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'End number is required';
                  }
                  final endNo = int.tryParse(value);
                  final startNo = int.tryParse(_pieceStartNoController.text);
                  if (endNo == null || endNo <= 0) {
                    return 'Enter a valid end number';
                  }
                  if (startNo != null && endNo <= startNo) {
                    return 'End number must be greater than start number';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Row 6: Rate per piece and Total pieces (calculated)
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _ratePerPieceController,
                decoration: const InputDecoration(
                  labelText: 'Rate per Piece *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Rate is required';
                  }
                  final rate = double.tryParse(value);
                  if (rate == null || rate <= 0) {
                    return 'Enter a valid rate';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCalculatedField(),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Row 7: Checked By
        CustomTextField(
          controller: _checkedByController,
          label: 'Checked By *',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Checked by is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Row 8: Remarks
        CustomTextField(
          controller: _remarksController,
          label: 'Remarks',
          maxLines: 3,
          textInputAction: TextInputAction.newline,
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date *',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          DateFormat('dd/MM/yyyy').format(_selectedDate),
          style: AppTextStyles.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildSizeDropdown() {
    return DropdownButtonFormField<GarmentSize>(
      value: _selectedSize,
      decoration: const InputDecoration(
        labelText: 'Size *',
        border: OutlineInputBorder(),
      ),
      items: GarmentSize.values.map((size) {
        return DropdownMenuItem(
          value: size,
          child: Text(size.displayName),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedSize = value;
          });
        }
      },
      validator: (value) {
        if (value == null) {
          return 'Size is required';
        }
        return null;
      },
    );
  }

  Widget _buildCalculatedField() {
    final startNo = int.tryParse(_pieceStartNoController.text) ?? 0;
    final endNo = int.tryParse(_pieceEndNoController.text) ?? 0;
    final totalPieces = endNo > startNo ? endNo - startNo + 1 : 0;
    final rate = double.tryParse(_ratePerPieceController.text) ?? 0.0;
    final totalAmount = totalPieces * rate;

    return InputDecorator(
      decoration: const InputDecoration(
        labelText: 'Total Pieces & Amount',
        border: OutlineInputBorder(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pieces: $totalPieces',
            style: AppTextStyles.bodyMedium,
          ),
          Text(
            'Amount: ₹${totalAmount.toStringAsFixed(2)}',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _submitForm,
            child: Text(widget.task == null ? 'Create Task' : 'Update Task'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final taskData = {
        'date': _selectedDate,
        'orderNo': _orderNoController.text.trim(),
        'clientName': _clientNameController.text.trim(),
        'designNo': _designNoController.text.trim(),
        'size': _selectedSize,
        'color': _colorController.text.trim(),
        'bundleNo': _bundleNoController.text.trim(),
        'pieceStartNo': int.parse(_pieceStartNoController.text),
        'pieceEndNo': int.parse(_pieceEndNoController.text),
        'ratePerPiece': double.parse(_ratePerPieceController.text),
        'checkedBy': _checkedByController.text.trim(),
        'remarks': _remarksController.text.trim(),
      };

      widget.onSubmit(taskData);
    }
  }
}
