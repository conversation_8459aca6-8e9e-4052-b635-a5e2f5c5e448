import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/numbering_task.dart';
import '../models/numbering_task_model.dart';

/// Remote data source for numbering tasks using Firebase Firestore
@injectable
class NumberingRemoteDataSource {
  final FirebaseFirestore _firestore;
  static const String _collection = 'numbering_tasks';
  static const String _settingsCollection = 'numbering_settings';

  NumberingRemoteDataSource(this._firestore);

  /// Get all numbering tasks with optional filtering
  Future<List<NumberingTaskModel>> getAllTasks({
    NumberingTaskStatus? status,
    String? orderNo,
    String? clientName,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (orderNo != null && orderNo.isNotEmpty) {
        query = query.where('order_no', isEqualTo: orderNo);
      }
      if (clientName != null && clientName.isNotEmpty) {
        query = query.where('client_name', isGreaterThanOrEqualTo: clientName)
            .where('client_name', isLessThan: clientName + '\uf8ff');
      }
      if (startDate != null) {
        query = query.where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply pagination
      if (offset != null && offset > 0) {
        query = query.offset(offset);
      }
      if (limit != null && limit > 0) {
        query = query.limit(limit);
      }

      // Order by creation date (newest first)
      query = query.orderBy('created_at', descending: true);

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => NumberingTaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch numbering tasks: $e');
    }
  }

  /// Get a specific numbering task by ID
  Future<NumberingTaskModel> getTaskById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (!doc.exists) {
        throw NotFoundException('Numbering task not found');
      }
      return NumberingTaskModel.fromFirestore(doc);
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to fetch numbering task: $e');
    }
  }

  /// Create a new numbering task
  Future<NumberingTaskModel> createTask(NumberingTaskModel task) async {
    try {
      final docRef = _firestore.collection(_collection).doc();
      final taskWithId = task.copyWith(id: docRef.id);
      await docRef.set(taskWithId.toFirestore());
      return taskWithId;
    } catch (e) {
      throw ServerException('Failed to create numbering task: $e');
    }
  }

  /// Update an existing numbering task
  Future<NumberingTaskModel> updateTask(NumberingTaskModel task) async {
    try {
      await _firestore.collection(_collection).doc(task.id).update(task.toFirestore());
      return task;
    } catch (e) {
      throw ServerException('Failed to update numbering task: $e');
    }
  }

  /// Delete a numbering task
  Future<void> deleteTask(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
    } catch (e) {
      throw ServerException('Failed to delete numbering task: $e');
    }
  }

  /// Get a new task (assign to user)
  Future<NumberingTaskModel> getNewTask(String userId) async {
    try {
      // Find the oldest pending task
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: NumberingTaskStatus.pending.value)
          .where('assigned_to', isNull: true)
          .orderBy('created_at')
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw NotFoundException('No pending tasks available');
      }

      final doc = querySnapshot.docs.first;
      final task = NumberingTaskModel.fromFirestore(doc);

      // Assign task to user and update status
      final updatedTask = task.copyWith(
        assignedTo: userId,
        status: NumberingTaskStatus.inProgress,
        updatedAt: DateTime.now(),
        updatedBy: userId,
      );

      await _firestore.collection(_collection).doc(task.id).update(updatedTask.toFirestore());
      return updatedTask;
    } catch (e) {
      if (e is NotFoundException) rethrow;
      throw ServerException('Failed to get new task: $e');
    }
  }

  /// Complete a numbering task
  Future<NumberingTaskModel> completeTask(String taskId, String userId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(taskId).get();
      if (!doc.exists) {
        throw NotFoundException('Numbering task not found');
      }

      final task = NumberingTaskModel.fromFirestore(doc);
      if (task.status != NumberingTaskStatus.inProgress) {
        throw ValidationException('Task must be in progress to complete');
      }

      final updatedTask = task.copyWith(
        status: NumberingTaskStatus.completed,
        completedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        updatedBy: userId,
      );

      await _firestore.collection(_collection).doc(taskId).update(updatedTask.toFirestore());
      return updatedTask;
    } catch (e) {
      if (e is NotFoundException || e is ValidationException) rethrow;
      throw ServerException('Failed to complete task: $e');
    }
  }

  /// Send task to Munda department
  Future<NumberingTaskModel> sendTaskToMunda(String taskId, String userId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(taskId).get();
      if (!doc.exists) {
        throw NotFoundException('Numbering task not found');
      }

      final task = NumberingTaskModel.fromFirestore(doc);
      if (task.status != NumberingTaskStatus.completed) {
        throw ValidationException('Task must be completed before sending to Munda');
      }

      final updatedTask = task.copyWith(
        status: NumberingTaskStatus.sentToMunda,
        sentToMundaAt: DateTime.now(),
        updatedAt: DateTime.now(),
        updatedBy: userId,
      );

      await _firestore.collection(_collection).doc(taskId).update(updatedTask.toFirestore());
      return updatedTask;
    } catch (e) {
      if (e is NotFoundException || e is ValidationException) rethrow;
      throw ServerException('Failed to send task to Munda: $e');
    }
  }

  /// Get tasks assigned to a specific user
  Future<List<NumberingTaskModel>> getTasksAssignedToUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('assigned_to', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => NumberingTaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch user tasks: $e');
    }
  }

  /// Get tasks by status
  Future<List<NumberingTaskModel>> getTasksByStatus(NumberingTaskStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.value)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => NumberingTaskModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException('Failed to fetch tasks by status: $e');
    }
  }

  /// Search tasks by criteria
  Future<List<NumberingTaskModel>> searchTasks({
    String? searchQuery,
    NumberingTaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply filters
      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (startDate != null) {
        query = query.where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      // Apply pagination
      if (offset != null && offset > 0) {
        query = query.offset(offset);
      }
      if (limit != null && limit > 0) {
        query = query.limit(limit);
      }

      query = query.orderBy('created_at', descending: true);

      final querySnapshot = await query.get();
      List<NumberingTaskModel> tasks = querySnapshot.docs
          .map((doc) => NumberingTaskModel.fromFirestore(doc))
          .toList();

      // Apply text search filter (client-side for now)
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final lowerQuery = searchQuery.toLowerCase();
        tasks = tasks.where((task) =>
            task.orderNo.toLowerCase().contains(lowerQuery) ||
            task.clientName.toLowerCase().contains(lowerQuery) ||
            task.designNo.toLowerCase().contains(lowerQuery) ||
            task.bundleNo.toLowerCase().contains(lowerQuery) ||
            task.checkedBy.toLowerCase().contains(lowerQuery)
        ).toList();
      }

      return tasks;
    } catch (e) {
      throw ServerException('Failed to search tasks: $e');
    }
  }

  /// Get default rate per piece
  Future<double> getDefaultRatePerPiece() async {
    try {
      final doc = await _firestore.collection(_settingsCollection).doc('default_rates').get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return (data['rate_per_piece'] as num?)?.toDouble() ?? 2.0;
      }
      return 2.0; // Default fallback rate
    } catch (e) {
      return 2.0; // Return default rate on error
    }
  }

  /// Update default rate per piece (admin only)
  Future<void> updateDefaultRatePerPiece(double rate, String adminUserId) async {
    try {
      await _firestore.collection(_settingsCollection).doc('default_rates').set({
        'rate_per_piece': rate,
        'updated_by': adminUserId,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      throw ServerException('Failed to update default rate: $e');
    }
  }

  /// Stream of real-time task updates
  Stream<List<NumberingTaskModel>> watchTasks({
    NumberingTaskStatus? status,
    String? userId,
  }) {
    try {
      Query query = _firestore.collection(_collection);

      if (status != null) {
        query = query.where('status', isEqualTo: status.value);
      }
      if (userId != null) {
        query = query.where('assigned_to', isEqualTo: userId);
      }

      query = query.orderBy('created_at', descending: true);

      return query.snapshots().map((snapshot) =>
          snapshot.docs.map((doc) => NumberingTaskModel.fromFirestore(doc)).toList()
      );
    } catch (e) {
      throw ServerException('Failed to watch tasks: $e');
    }
  }
}
