import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/numbering_task.dart';
import '../../domain/repositories/numbering_repository.dart';
import '../datasources/numbering_local_datasource.dart';
import '../datasources/numbering_remote_datasource.dart';
import '../models/numbering_task_model.dart';

/// Implementation of NumberingRepository
@LazySingleton(as: NumberingRepository)
class NumberingRepositoryImpl implements NumberingRepository {
  final NumberingRemoteDataSource _remoteDataSource;
  final NumberingLocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;

  NumberingRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<NumberingTask>>> getAllTasks({
    NumberingTaskStatus? status,
    String? orderNo,
    String? clientName,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        // Fetch from remote and cache
        final remoteTasks = await _remoteDataSource.getAllTasks(
          status: status,
          orderNo: orderNo,
          clientName: clientName,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          offset: offset,
        );
        
        // Cache the results
        await _localDataSource.cacheTasks(remoteTasks);
        
        return Right(remoteTasks.map((model) => model.toEntity()).toList());
      } else {
        // Fetch from cache when offline
        final cachedTasks = await _localDataSource.searchTasks(
          searchQuery: null,
          status: status,
          startDate: startDate,
          endDate: endDate,
        );
        
        return Right(cachedTasks.map((model) => model.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> getTaskById(String id) async {
    try {
      if (await _networkInfo.isConnected) {
        final remoteTask = await _remoteDataSource.getTaskById(id);
        await _localDataSource.cacheTask(remoteTask);
        return Right(remoteTask.toEntity());
      } else {
        final cachedTask = await _localDataSource.getTaskById(id);
        if (cachedTask == null) {
          return const Left(CacheFailure('Task not found in cache'));
        }
        return Right(cachedTask.toEntity());
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get task: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> createTask(NumberingTask task) async {
    try {
      final taskModel = NumberingTaskModel.fromEntity(task);
      
      if (await _networkInfo.isConnected) {
        final createdTask = await _remoteDataSource.createTask(taskModel);
        await _localDataSource.cacheTask(createdTask);
        return Right(createdTask.toEntity());
      } else {
        // Store locally and mark for sync
        await _localDataSource.cacheTask(taskModel);
        await _localDataSource.markTaskForSync(task.id);
        return Right(task);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to create task: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> updateTask(NumberingTask task) async {
    try {
      final taskModel = NumberingTaskModel.fromEntity(task);
      
      if (await _networkInfo.isConnected) {
        final updatedTask = await _remoteDataSource.updateTask(taskModel);
        await _localDataSource.cacheTask(updatedTask);
        return Right(updatedTask.toEntity());
      } else {
        // Store locally and mark for sync
        await _localDataSource.cacheTask(taskModel);
        await _localDataSource.markTaskForSync(task.id);
        return Right(task);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to update task: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteTask(String id) async {
    try {
      if (await _networkInfo.isConnected) {
        await _remoteDataSource.deleteTask(id);
        await _localDataSource.removeTask(id);
        return const Right(null);
      } else {
        // Mark for deletion when online
        await _localDataSource.removeTask(id);
        await _localDataSource.markTaskForSync(id);
        return const Right(null);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to delete task: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> getNewTask(String userId) async {
    try {
      if (await _networkInfo.isConnected) {
        final newTask = await _remoteDataSource.getNewTask(userId);
        await _localDataSource.cacheTask(newTask);
        return Right(newTask.toEntity());
      } else {
        return const Left(NetworkFailure('Cannot assign new task while offline'));
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get new task: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> completeTask(String taskId, String userId) async {
    try {
      if (await _networkInfo.isConnected) {
        final completedTask = await _remoteDataSource.completeTask(taskId, userId);
        await _localDataSource.cacheTask(completedTask);
        return Right(completedTask.toEntity());
      } else {
        // Update locally and mark for sync
        final cachedTask = await _localDataSource.getTaskById(taskId);
        if (cachedTask == null) {
          return const Left(CacheFailure('Task not found in cache'));
        }
        
        final updatedTask = cachedTask.copyWith(
          status: NumberingTaskStatus.completed,
          completedAt: DateTime.now(),
          updatedAt: DateTime.now(),
          updatedBy: userId,
        );
        
        await _localDataSource.cacheTask(updatedTask);
        await _localDataSource.markTaskForSync(taskId);
        return Right(updatedTask.toEntity());
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to complete task: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTask>> sendTaskToMunda(String taskId, String userId) async {
    try {
      if (await _networkInfo.isConnected) {
        final sentTask = await _remoteDataSource.sendTaskToMunda(taskId, userId);
        await _localDataSource.cacheTask(sentTask);
        return Right(sentTask.toEntity());
      } else {
        // Update locally and mark for sync
        final cachedTask = await _localDataSource.getTaskById(taskId);
        if (cachedTask == null) {
          return const Left(CacheFailure('Task not found in cache'));
        }
        
        final updatedTask = cachedTask.copyWith(
          status: NumberingTaskStatus.sentToMunda,
          sentToMundaAt: DateTime.now(),
          updatedAt: DateTime.now(),
          updatedBy: userId,
        );
        
        await _localDataSource.cacheTask(updatedTask);
        await _localDataSource.markTaskForSync(taskId);
        return Right(updatedTask.toEntity());
      }
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to send task to Munda: $e'));
    }
  }

  @override
  Future<Either<Failure, List<NumberingTask>>> getTasksAssignedToUser(String userId) async {
    try {
      if (await _networkInfo.isConnected) {
        final remoteTasks = await _remoteDataSource.getTasksAssignedToUser(userId);
        await _localDataSource.batchUpdateTasks(remoteTasks);
        return Right(remoteTasks.map((model) => model.toEntity()).toList());
      } else {
        final cachedTasks = await _localDataSource.getTasksAssignedToUser(userId);
        return Right(cachedTasks.map((model) => model.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get user tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, List<NumberingTask>>> getTasksByStatus(NumberingTaskStatus status) async {
    try {
      if (await _networkInfo.isConnected) {
        final remoteTasks = await _remoteDataSource.getTasksByStatus(status);
        await _localDataSource.batchUpdateTasks(remoteTasks);
        return Right(remoteTasks.map((model) => model.toEntity()).toList());
      } else {
        final cachedTasks = await _localDataSource.getTasksByStatus(status);
        return Right(cachedTasks.map((model) => model.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get tasks by status: $e'));
    }
  }

  @override
  Future<Either<Failure, NumberingTaskStatistics>> getTaskStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  }) async {
    try {
      // For now, calculate statistics from cached data
      final tasks = await _localDataSource.getAllTasks();
      
      // Filter tasks based on criteria
      final filteredTasks = tasks.where((task) {
        if (startDate != null && task.date.isBefore(startDate)) return false;
        if (endDate != null && task.date.isAfter(endDate)) return false;
        if (userId != null && task.assignedTo != userId) return false;
        return true;
      }).toList();

      final statistics = NumberingTaskStatistics(
        totalTasks: filteredTasks.length,
        pendingTasks: filteredTasks.where((t) => t.status == NumberingTaskStatus.pending).length,
        inProgressTasks: filteredTasks.where((t) => t.status == NumberingTaskStatus.inProgress).length,
        completedTasks: filteredTasks.where((t) => t.status == NumberingTaskStatus.completed).length,
        sentToMundaTasks: filteredTasks.where((t) => t.status == NumberingTaskStatus.sentToMunda).length,
        totalAmount: filteredTasks.fold(0.0, (sum, task) => sum + (task.pieceEndNo - task.pieceStartNo + 1) * task.ratePerPiece),
        averageRatePerPiece: filteredTasks.isNotEmpty 
            ? filteredTasks.fold(0.0, (sum, task) => sum + task.ratePerPiece) / filteredTasks.length 
            : 0.0,
        totalPieces: filteredTasks.fold(0, (sum, task) => sum + (task.pieceEndNo - task.pieceStartNo + 1)),
        tasksByDate: {},
        tasksByUser: {},
      );

      return Right(statistics);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get task statistics: $e'));
    }
  }

  @override
  Future<Either<Failure, List<NumberingTask>>> bulkUpdateTaskStatus(
    List<String> taskIds,
    NumberingTaskStatus status,
    String userId,
  ) async {
    try {
      // This would need to be implemented based on specific requirements
      return const Left(UnknownFailure('Bulk update not implemented yet'));
    } catch (e) {
      return Left(UnknownFailure('Failed to bulk update tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, List<NumberingTask>>> searchTasks({
    String? searchQuery,
    NumberingTaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        final remoteTasks = await _remoteDataSource.searchTasks(
          searchQuery: searchQuery,
          status: status,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          offset: offset,
        );
        
        await _localDataSource.batchUpdateTasks(remoteTasks);
        return Right(remoteTasks.map((model) => model.toEntity()).toList());
      } else {
        final cachedTasks = await _localDataSource.searchTasks(
          searchQuery: searchQuery,
          status: status,
          startDate: startDate,
          endDate: endDate,
        );
        
        return Right(cachedTasks.map((model) => model.toEntity()).toList());
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to search tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, double>> getDefaultRatePerPiece() async {
    try {
      if (await _networkInfo.isConnected) {
        final rate = await _remoteDataSource.getDefaultRatePerPiece();
        await _localDataSource.cacheDefaultRatePerPiece(rate);
        return Right(rate);
      } else {
        final cachedRate = await _localDataSource.getDefaultRatePerPiece();
        return Right(cachedRate);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to get default rate: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateDefaultRatePerPiece(double rate, String adminUserId) async {
    try {
      if (await _networkInfo.isConnected) {
        await _remoteDataSource.updateDefaultRatePerPiece(rate, adminUserId);
        await _localDataSource.cacheDefaultRatePerPiece(rate);
        return const Right(null);
      } else {
        return const Left(NetworkFailure('Cannot update default rate while offline'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure('Failed to update default rate: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> exportTasks({
    DateTime? startDate,
    DateTime? endDate,
    NumberingTaskStatus? status,
    String format = 'csv',
  }) async {
    try {
      // This would need to be implemented based on specific requirements
      return const Left(UnknownFailure('Export functionality not implemented yet'));
    } catch (e) {
      return Left(UnknownFailure('Failed to export tasks: $e'));
    }
  }

  @override
  Future<Either<Failure, List<NumberingTaskHistory>>> getTaskHistory(String taskId) async {
    try {
      // This would need to be implemented based on specific requirements
      return const Left(UnknownFailure('Task history not implemented yet'));
    } catch (e) {
      return Left(UnknownFailure('Failed to get task history: $e'));
    }
  }

  @override
  Stream<Either<Failure, List<NumberingTask>>> watchTasks({
    NumberingTaskStatus? status,
    String? userId,
  }) {
    try {
      return _remoteDataSource.watchTasks(status: status, userId: userId)
          .map((tasks) => Right<Failure, List<NumberingTask>>(
              tasks.map((model) => model.toEntity()).toList()))
          .handleError((error) => Left<Failure, List<NumberingTask>>(
              ServerFailure('Failed to watch tasks: $error')));
    } catch (e) {
      return Stream.value(Left(UnknownFailure('Failed to watch tasks: $e')));
    }
  }

  @override
  Stream<Either<Failure, NumberingTaskStatistics>> watchTaskStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  }) {
    try {
      // This would need to be implemented based on specific requirements
      return Stream.value(const Left(UnknownFailure('Watch statistics not implemented yet')));
    } catch (e) {
      return Stream.value(Left(UnknownFailure('Failed to watch statistics: $e')));
    }
  }
}
