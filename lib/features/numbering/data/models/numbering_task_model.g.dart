// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'numbering_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NumberingTaskModel _$NumberingTaskModelFromJson(Map<String, dynamic> json) =>
    NumberingTaskModel(
      id: json['id'] as String,
      date: NumberingTaskModel._dateFromJson(json['date']),
      orderNo: json['order_no'] as String,
      clientName: json['client_name'] as String,
      designNo: json['design_no'] as String,
      size: NumberingTaskModel._sizeFromJson(json['size'] as String),
      color: json['color'] as String,
      bundleNo: json['bundle_no'] as String,
      pieceStartNo: json['piece_start_no'] as int,
      pieceEndNo: json['piece_end_no'] as int,
      ratePerPiece: (json['rate_per_piece'] as num).toDouble(),
      checkedBy: json['checked_by'] as String,
      remarks: json['remarks'] as String,
      status: NumberingTaskModel._statusFromJson(json['status'] as String),
      assignedTo: json['assigned_to'] as String?,
      completedAt: NumberingTaskModel._dateTimeFromJson(json['completed_at']),
      sentToMundaAt:
          NumberingTaskModel._dateTimeFromJson(json['sent_to_munda_at']),
      createdAt: NumberingTaskModel._dateFromJson(json['created_at']),
      updatedAt: NumberingTaskModel._dateFromJson(json['updated_at']),
      createdBy: json['created_by'] as String,
      updatedBy: json['updated_by'] as String,
    );

Map<String, dynamic> _$NumberingTaskModelToJson(NumberingTaskModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': NumberingTaskModel._dateToJson(instance.date),
      'order_no': instance.orderNo,
      'client_name': instance.clientName,
      'design_no': instance.designNo,
      'size': NumberingTaskModel._sizeToJson(instance.size),
      'color': instance.color,
      'bundle_no': instance.bundleNo,
      'piece_start_no': instance.pieceStartNo,
      'piece_end_no': instance.pieceEndNo,
      'rate_per_piece': instance.ratePerPiece,
      'checked_by': instance.checkedBy,
      'remarks': instance.remarks,
      'status': NumberingTaskModel._statusToJson(instance.status),
      'assigned_to': instance.assignedTo,
      'completed_at': NumberingTaskModel._dateTimeToJson(instance.completedAt),
      'sent_to_munda_at':
          NumberingTaskModel._dateTimeToJson(instance.sentToMundaAt),
      'created_at': NumberingTaskModel._dateToJson(instance.createdAt),
      'updated_at': NumberingTaskModel._dateToJson(instance.updatedAt),
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

NumberingTaskStatisticsModel _$NumberingTaskStatisticsModelFromJson(
        Map<String, dynamic> json) =>
    NumberingTaskStatisticsModel(
      totalTasks: json['total_tasks'] as int,
      pendingTasks: json['pending_tasks'] as int,
      inProgressTasks: json['in_progress_tasks'] as int,
      completedTasks: json['completed_tasks'] as int,
      sentToMundaTasks: json['sent_to_munda_tasks'] as int,
      totalAmount: (json['total_amount'] as num).toDouble(),
      averageRatePerPiece: (json['average_rate_per_piece'] as num).toDouble(),
      totalPieces: json['total_pieces'] as int,
      tasksByDate: Map<String, int>.from(json['tasks_by_date'] as Map),
      tasksByUser: Map<String, int>.from(json['tasks_by_user'] as Map),
    );

Map<String, dynamic> _$NumberingTaskStatisticsModelToJson(
        NumberingTaskStatisticsModel instance) =>
    <String, dynamic>{
      'total_tasks': instance.totalTasks,
      'pending_tasks': instance.pendingTasks,
      'in_progress_tasks': instance.inProgressTasks,
      'completed_tasks': instance.completedTasks,
      'sent_to_munda_tasks': instance.sentToMundaTasks,
      'total_amount': instance.totalAmount,
      'average_rate_per_piece': instance.averageRatePerPiece,
      'total_pieces': instance.totalPieces,
      'tasks_by_date': instance.tasksByDate,
      'tasks_by_user': instance.tasksByUser,
    };
